import 'package:flutter/material.dart';
import 'package:scribeblogs/Utility/colors.dart';


class DrawerTile extends StatefulWidget {
  final IconData icon;
  final String label;
  final bool selected;
  final VoidCallback onTap;
  final Duration animationDuration;

  const DrawerTile({
    super.key,
    required this.icon,
    required this.label,
    required this.selected,
    required this.onTap,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  State<DrawerTile> createState() => _DrawerTileState();
}

class _DrawerTileState extends State<DrawerTile> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {




    Color targetBackgroundColor = Colors.white;
    Color targetIconColor = AppColors.textDark;
    Color targetTextColor = AppColors.textDark;

    if (widget.selected) {
      targetBackgroundColor = AppColors.primaryDark;
      targetIconColor = AppColors.accentLight;
      targetTextColor = AppColors.accentLight;
    } else if (_isHovered) {
      targetBackgroundColor = AppColors.primaryDark.withOpacity(0.5);
      targetIconColor = AppColors.accentLight;
      targetTextColor = AppColors.accentLight;
    }

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: widget.animationDuration,
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          decoration: BoxDecoration(
            color: targetBackgroundColor,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Row(
            children: <Widget>[
              AnimatedTheme(
                data: ThemeData(
                  iconTheme: IconThemeData(
                    color: targetIconColor,
                    size: 24.0,
                  ),
                ),
                child: Icon(widget.icon),
              ),
              const SizedBox(width: 16.0),
              Expanded(
                child: AnimatedDefaultTextStyle(
                  duration: widget.animationDuration,
                  curve: Curves.easeInOut,
                  style: TextStyle(
                    color: targetTextColor,
                    fontWeight: widget.selected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 16,
                  ),
                  child: Text(widget.label),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}