import 'package:flutter/material.dart';
import 'package:scribeblogs/Utility/AppState.dart';
import 'package:scribeblogs/Views/Screen/admin_blogManagement.dart';
import 'package:scribeblogs/Views/Screen/admin_categories.dart';

import 'package:scribeblogs/Views/Screen/admin_announcements.dart';
import 'package:scribeblogs/Views/Screen/admin_dashboard.dart';
import 'package:scribeblogs/Views/Screen/admin_feedback.dart';
import 'package:scribeblogs/Views/Screen/admin_profile.dart';
import 'package:scribeblogs/Views/Screen/admin_usersManagement.dart';
import 'drawer_tile.dart';

class SBDrawer extends StatefulWidget {
  const SBDrawer({super.key});

  @override
  State<SBDrawer> createState() => _SBDrawerState();
}

class _SBDrawerState extends State<SBDrawer> {
  void onSelectItem(DrawerItems item) {
    setState(() {
      AppState.selectedDrawerItem = item;
    });

    // Your existing navigation logic...
    switch (AppState.selectedDrawerItem) {
      case DrawerItems.dashboard:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminDashboard()),
        );
        break;

      case DrawerItems.blogManagement:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminBlogManagement()),
        );
        break;

      case DrawerItems.categories:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminCategories()),
        );
        break;

      case DrawerItems.usersManagement:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminUsersmanagement()),
        );
        break;

      case DrawerItems.feedback:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminFeedback()),
        );
        break;

      case DrawerItems.announcements:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminAnnouncements()),
        );
        break;

      case DrawerItems.profile:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AdminProfile()),
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;


    if (screenWidth < 580) {
      return Container(); // Or return SizedBox.shrink() if preferred
    }

    return Container(
      width: 300,
      decoration: BoxDecoration(color: Colors.white),
      padding: EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      child: Column(
        children: [
          Image.asset(
            'assets/images/s_icon.png',
            height: 75,
            color: Colors.black,
          ),
          Text(
            'ScribeBlogs',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 24),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.dashboard
                ? Icons.dashboard
                : Icons.dashboard_outlined,
            label: 'Dashboard',
            selected: AppState.selectedDrawerItem == DrawerItems.dashboard,
            onTap: () => onSelectItem(DrawerItems.dashboard),
          ),
          SizedBox(height: 8),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.blogManagement
                ? Icons.document_scanner
                : Icons.document_scanner_outlined,
            label: 'Blog Management',
            selected: AppState.selectedDrawerItem == DrawerItems.blogManagement,
            onTap: () => onSelectItem(DrawerItems.blogManagement),
          ),
          SizedBox(height: 8),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.categories
                ? Icons.category
                : Icons.category_outlined,
            label: 'Category Management',
            selected: AppState.selectedDrawerItem == DrawerItems.categories,
            onTap: () => onSelectItem(DrawerItems.categories),
          ),
          SizedBox(height: 8),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.usersManagement
                ? Icons.people
                : Icons.people_outline,
            label: 'User Management',
            selected: AppState.selectedDrawerItem == DrawerItems.usersManagement,
            onTap: () => onSelectItem(DrawerItems.usersManagement),
          ),
          SizedBox(height: 8),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.feedback
                ? Icons.feedback
                : Icons.feedback_outlined,
            label: 'Feedback',
            selected: AppState.selectedDrawerItem == DrawerItems.feedback,
            onTap: () => onSelectItem(DrawerItems.feedback),
          ),
          SizedBox(height: 8),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.announcements
                ? Icons.campaign
                : Icons.campaign_outlined,
            label: 'Announcements',
            selected: AppState.selectedDrawerItem == DrawerItems.announcements,
            onTap: () => onSelectItem(DrawerItems.announcements),
          ),
          SizedBox(height: 8),
          DrawerTile(
            icon: AppState.selectedDrawerItem == DrawerItems.profile
                ? Icons.person
                : Icons.person_outlined,
            label: 'Profile',
            selected: AppState.selectedDrawerItem == DrawerItems.profile,
            onTap: () => onSelectItem(DrawerItems.profile),
          ),
        ],
      ),
    );
  }
}