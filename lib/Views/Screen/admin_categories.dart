import 'package:flutter/material.dart';

import 'package:scribeblogs/Views/Component/drawer.dart';

class AdminCategories extends StatefulWidget {
  const AdminCategories({super.key});

  @override
  State<AdminCategories> createState() => _AdminCategoriesState();
}

class _AdminCategoriesState extends State<AdminCategories> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          SBDrawer(),
          Column(children: [Text('Categories')]),
        ],
      ),
    );
  }
}
