import 'package:flutter/material.dart';
import 'package:scribeblogs/Utility/colors.dart';
import 'package:scribeblogs/Views/Component/drawer.dart';

class AdminBlogManagement extends StatefulWidget {
  const AdminBlogManagement({super.key});

  @override
  State<AdminBlogManagement> createState() => _AdminBlogManagementState();
}

class _AdminBlogManagementState extends State<AdminBlogManagement> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Custom breakpoints
    int crossAxisCount;
    double childAspectRatio;



    return Scaffold(
      body: Row(
        children: [
          const SBDrawer(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Blog Management',
                    style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                Wrap(
                  alignment: MediaQuery.of(context).size.width < 600 ? WrapAlignment.start : WrapAlignment.spaceBetween,
                  runAlignment: WrapAlignment.center,
                  spacing: 16,
                  runSpacing: 12,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600 ? double.infinity : 300,
                      child: _buildSearchField(MediaQuery.of(context).size.width < 600 ? double.infinity : 300),
                    ),
                    _buildAddButton(),
                  ],
                ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: GridView.builder(
                      itemCount: 10,
                      physics: const BouncingScrollPhysics(),
                     gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                      maxCrossAxisExtent: 400,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.85, // this controls height vs width
                    ),

                    itemBuilder: (context, index) =>
                          _buildBlogCard(screenWidth),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(double width) {
    return SizedBox(
      width: width,
      child: TextFormField(
        decoration: InputDecoration(
          hintText: 'Search Blogs',
          prefixIcon: const Icon(Icons.search),
          filled: true,
          fillColor: AppColors.neutralLight,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    return ElevatedButton(
      onPressed: () {},
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.accentLight,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: const Text('Add Blog', style: TextStyle(fontSize: 16)),
    );
  }

  Widget _buildBlogCard(double screenWidth) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.neutralLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'How to Learn a Language: The Complete System That Actually Works',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              'https://miro.medium.com/v2/resize:fit:2000/format:webp/1*VV2NXHe3zM0q9YFFcR6R4Q.png',
              height: screenWidth < 600 ? 120 : 150,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6, // horizontal spacing between items
            runSpacing: 4, // vertical spacing if wrapped
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              _buildTag('Tech'),
              const Icon(Icons.circle, size: 6),
              const Text('Published', style: TextStyle(fontSize: 12, color: Colors.grey)),
              const Icon(Icons.circle, size: 6),
              const Text('2 min read', style: TextStyle(fontSize: 12, color: Colors.grey)),
            ],
          ),
          const SizedBox(height: 8),
          const Expanded(
            child: Text(
              '20 science-based principles and strategies for building fluency — From a linguist, language teacher, and polyglot',
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 13),
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6, // horizontal spacing between items
            runSpacing: 4, // vertical spacing if wrapped
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              _buildActionButton('Edit', AppColors.accentLight, Colors.black),
              const SizedBox(width: 8),
              _buildActionButton('Delete', Colors.redAccent.withOpacity(0.1), Colors.red),
              const SizedBox(width: 8),
              _buildActionButton('Preview', Colors.blueAccent.withOpacity(0.1), Colors.blue),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.accentLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  Widget _buildActionButton(String label, Color bgColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(label, style: TextStyle(fontSize: 12, color: textColor)),
    );
  }
}



